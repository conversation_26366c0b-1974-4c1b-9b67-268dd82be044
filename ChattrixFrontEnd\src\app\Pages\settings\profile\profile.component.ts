import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, Observable, takeUntil } from 'rxjs';

import {
  UserProfileService,
  UserProfile,
} from '../../chattrix/Services/UserProfile.service';
import { ThemeService, ThemeMode } from '../../chattrix/Services/Theme.service';
import { NotificationService } from '../../../Core/Services/notification.service';
import {
  ProfileUpdateService,
  ProfileUpdateRequest,
} from '../Services/profile-update.service';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // User profile data
  userProfile$: Observable<UserProfile | null>;
  currentTheme$: Observable<ThemeMode>;

  // Form and UI state
  profileForm: FormGroup;
  isLoading = false;
  isSubmitting = false;

  // Tab navigation
  selectedTabIndex = 0;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private userProfileService: UserProfileService,
    private themeService: ThemeService,
    private notificationService: NotificationService,
    private profileUpdateService: ProfileUpdateService,
  ) {
    this.userProfile$ = this.userProfileService.userProfile$;
    this.currentTheme$ = this.themeService.currentTheme$;

    // Initialize form
    this.profileForm = this.createProfileForm();
  }

  ngOnInit(): void {
    // Subscribe to user profile changes and populate form
    this.userProfile$.pipe(takeUntil(this.destroy$)).subscribe((profile) => {
      if (profile) {
        this.populateForm(profile);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Creates the reactive form for profile editing
   */
  private createProfileForm(): FormGroup {
    return this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      phoneNumber: [''],
      description: [''],
    });
  }

  /**
   * Populates the form with current user profile data
   */
  private populateForm(profile: UserProfile): void {
    this.profileForm.patchValue({
      fullName: profile.name || '',
      phoneNumber: profile.phoneNumber || '',
      description: profile.description || '',
    });
  }

  /**
   * Gets the display name for the user
   */
  getUserDisplayName(profile: UserProfile | null): string {
    if (!profile) return 'User';
    return profile.displayName || profile.name || profile.email || 'User';
  }

  /**
   * Gets the user initials for avatar display
   */
  getUserInitials(profile: UserProfile | null): string {
    if (!profile) return 'U';
    return profile.initials || 'U';
  }

  /**
   * Gets the role display string
   */
  getRoleDisplay(profile: UserProfile | null): string {
    if (!profile) return 'User';

    if (Array.isArray(profile.role)) {
      return profile.role.join(', ');
    }
    return profile.role || 'User';
  }

  /**
   * Handles tab change navigation
   */
  onTabChange(index: number): void {
    this.selectedTabIndex = index;

    if (index === 1) {
      // Navigate to change password component
      this.router.navigate(['/settings/change-password']);
    }
  }

  /**
   * Handles profile form submission
   */
  onSubmitProfile(): void {
    if (this.profileForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      const updateData: ProfileUpdateRequest = {
        fullName: this.profileForm.value.fullName?.trim(),
        phoneNumber: this.profileForm.value.phoneNumber?.trim() || undefined,
        description: this.profileForm.value.description?.trim() || undefined,
      };

      this.profileUpdateService
        .updateProfile(updateData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.isSuccess) {
              this.notificationService.showSuccess(
                'Profile updated successfully!',
              );
              // Optionally refresh user profile data
              // this.userProfileService.refreshProfile();
            } else {
              this.notificationService.showError(
                response.message || 'Failed to update profile',
              );
            }
          },
          error: (error) => {
            this.notificationService.showError(
              error.message || 'An error occurred while updating profile',
            );
          },
          complete: () => {
            this.isSubmitting = false;
          },
        });
    } else {
      this.markFormGroupTouched();
    }
  }

  /**
   * Handles form cancellation
   */
  onCancelProfile(): void {
    // Reset form to original values
    const currentProfile = this.userProfileService.currentProfile;
    if (currentProfile) {
      this.populateForm(currentProfile);
    }
    this.profileForm.markAsUntouched();
    this.router.navigate(['/dashboard']);
  }

  /**
   * Marks all form fields as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach((key) => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Gets form field error message
   */
  getFieldError(fieldName: string): string {
    const control = this.profileForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (control.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${control.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }
}
