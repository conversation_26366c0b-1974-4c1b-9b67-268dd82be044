using ChattrixBackend.Core.Entities.UserManagement.ChangePasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.ForgotPasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.LoginModel;
using ChattrixBackend.Core.Entities.UserManagement.RegisterModel;
using ChattrixBackend.Core.Entities.UserManagement.ResetPasswordModel;
using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.Core.Entities.UserManagement.ToggleStatusRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.UserDetailsModel;
using ChattrixBackend.Core.Entities.UserManagement.VerifyOtpRequestModel;
using ChattrixBackend.Core.Entities.UserManagement.VerifyResetTokenModel;
using ChattrixBackend.Core.Pagination.PagedResponseModel;
using ChattrixBackend.Core.Pagination.PaginationParametersModel;
using Microsoft.AspNetCore.Http;

namespace ChattrixBackend.Services.AccountServices {
    public interface IAccountService {

        Task<Response> CreateAdminUserAsync(AddUser register);
        Task<Response> RegisterUserAsync(AddUser register);
        Task<Response> AddUserAsync(AddUser register);
        Task<Response> UpdateUserAsync(string userId, UserDetails userDetails);
        Task<Response> DeleteUserAsync(string userId, ToggleStatusRequest request, string currentUserId);
        Task<Response> LoginAsync(Login login);
        Task<Response> VerifyOtpAsync(VerifyOtpRequest request);
        Task<Response> ResendOtpAsync(string userId);
        Task<UserDetails?> GetUserByIdAsync(string userId);
        Task<List<UserDetails>> GetAllUsersAsync();
        Task<PagedResponse<UserDetails>> GetPagedUsersAsync(PaginationParameters parameters);
        Task<Response> ChangePasswordAsync(ChangePasswordRequest request);
        Task<Response> InitiateForgotPasswordAsync(ForgotPasswordRequest request);
        Task<Response> VerifyResetTokenAsync(VerifyResetTokenRequest request);
        Task<Response> ResetPasswordAsync(ResetPasswordRequest request);
        // File Operations
        Task<string> UploadFile(IFormFile file, string folderName);
        Task<Response> DeleteSQlUser(string userId);
    }
}
